<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>远程控制码管理中心</title>
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- 引入Font Awesome -->
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- 配置Tailwind自定义颜色和字体 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#36D399',
            warning: '#FF9F1C',
            danger: '#F87272',
            dark: '#1E293B',
            light: '#F8FAFC'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .tab-active {
        @apply border-primary text-primary font-medium;
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
      }
      .btn-effect {
        @apply transition-all duration-200 transform hover:scale-105 active:scale-95;
      }
      .fade-in {
        animation: fadeIn 0.3s ease-in-out;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
      .group-actions {
        @apply absolute -top-1 -right-1 hidden group-hover:flex space-x-1 z-10;
      }
      .group-action-btn {
        @apply w-5 h-5 text-white rounded-full text-xs flex items-center justify-center transition-all duration-200 hover:scale-110;
      }
      .nav-tab {
        @apply text-gray-400 hover:text-primary hover:bg-primary/5;
      }
      .nav-tab-active {
        @apply text-primary bg-primary/10;
      }
    }
  </style>
</head>

<body class="font-inter bg-gray-50 text-dark min-h-screen flex flex-col">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="container mx-auto px-4 py-4 flex flex-wrap items-center justify-between">
      <div class="flex items-center space-x-2">
        <i class="fa fa-server text-primary text-2xl"></i>
        <h1 class="text-xl md:text-2xl font-bold text-primary">远程控制码管理中心</h1>
      </div>
      
      <div class="flex items-center space-x-3 mt-2 sm:mt-0">
        <button id="add-group-btn" class="btn-effect flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm">
          <i class="fa fa-folder-open-o mr-1"></i> 新增分组
        </button>
        <button id="add-device-btn" class="btn-effect flex items-center px-4 py-2 bg-primary text-white rounded-lg text-sm shadow-sm hover:bg-primary/90">
          <i class="fa fa-plus mr-1"></i> 新增设备
        </button>
      </div>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <!-- 设备管理页面 -->
    <div id="devices-page">
      <!-- 分组选项卡 -->
      <div class="mb-6 overflow-x-auto scrollbar-hide">
        <div class="flex space-x-1 pb-2 min-w-max" id="group-tabs">
          <button class="tab-active px-4 py-2 rounded-t-lg border-b-2 border-transparent" data-group="all">
            <i class="fa fa-th mr-1"></i> 全部设备
          </button>
          <!-- 分组将通过JS动态添加 -->
        </div>
      </div>

      <!-- 设备列表区域 -->
      <div id="devices-container" class="grid grid-cols-2 gap-4">
        <!-- 设备卡片将通过JS动态添加 -->
        <div class="col-span-full flex items-center justify-center h-40 text-gray-400">
          <div class="text-center">
            <i class="fa fa-mobile text-5xl mb-3 opacity-30"></i>
            <p>暂无设备数据，请添加设备</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 统计页面 -->
    <div id="statistics-page" class="hidden">
      <div class="mb-6">
        <h2 class="text-2xl font-bold text-dark mb-2">数据统计</h2>
        <p class="text-gray-500">查看设备和分组的统计信息</p>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- 总设备数 -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500 mb-1">总设备数</p>
              <p id="total-devices" class="text-3xl font-bold text-primary">0</p>
            </div>
            <div class="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
              <i class="fa fa-mobile text-primary text-xl"></i>
            </div>
          </div>
        </div>

        <!-- 分组数量 -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500 mb-1">分组数量</p>
              <p id="total-groups" class="text-3xl font-bold text-secondary">0</p>
            </div>
            <div class="w-12 h-12 bg-secondary/10 rounded-lg flex items-center justify-center">
              <i class="fa fa-folder-o text-secondary text-xl"></i>
            </div>
          </div>
        </div>

        <!-- 室内设备 -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500 mb-1">室内设备</p>
              <p id="indoor-devices" class="text-3xl font-bold text-blue-600">0</p>
            </div>
            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
              <i class="fa fa-home text-blue-600 text-xl"></i>
            </div>
          </div>
        </div>

        <!-- 标准设备 -->
        <div class="bg-white rounded-xl shadow-sm p-6 border border-gray-100">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm text-gray-500 mb-1">标准设备</p>
              <p id="standard-devices" class="text-3xl font-bold text-green-600">0</p>
            </div>
            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
              <i class="fa fa-desktop text-green-600 text-xl"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- 分组详细统计 -->
      <div class="bg-white rounded-xl shadow-sm border border-gray-100">
        <div class="p-6 border-b border-gray-100">
          <h3 class="text-lg font-bold text-dark">分组详细统计</h3>
        </div>
        <div class="p-6">
          <div id="group-statistics" class="space-y-4">
            <!-- 分组统计将通过JS动态添加 -->
          </div>
        </div>
      </div>
    </div>
  </main>

  <!-- 底部导航栏 -->
  <nav class="bg-white border-t border-gray-200 sticky bottom-0 z-40">
    <div class="container mx-auto px-4">
      <div class="flex justify-center space-x-8 py-3">
        <button id="nav-devices" class="nav-tab nav-tab-active flex flex-col items-center py-2 px-4 rounded-lg transition-all">
          <i class="fa fa-mobile text-xl mb-1"></i>
          <span class="text-xs">设备管理</span>
        </button>
        <button id="nav-statistics" class="nav-tab flex flex-col items-center py-2 px-4 rounded-lg transition-all">
          <i class="fa fa-bar-chart text-xl mb-1"></i>
          <span class="text-xs">统计</span>
        </button>
      </div>
    </div>
  </nav>

  <!-- 页脚 -->
  <footer class="bg-white border-t mt-8 py-4">
    <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
      <p>远程控制码管理中心 &copy; 2023</p>
    </div>
  </footer>

  <!-- 新增/编辑设备模态框 -->
  <div id="device-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center fade-in">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-md p-6 m-4 transform transition-all">
      <div class="flex justify-between items-center mb-4">
        <h2 id="modal-title" class="text-xl font-bold text-dark">新增设备</h2>
        <button id="close-modal" class="text-gray-400 hover:text-gray-600">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>
      
      <form id="device-form">
        <input type="hidden" id="device-id">
        
        <div class="mb-4">
          <label for="device-name" class="block text-sm font-medium text-gray-700 mb-1">设备名称 *</label>
          <input type="text" id="device-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>
        
        <div class="mb-4">
          <label for="device-group" class="block text-sm font-medium text-gray-700 mb-1">所属分组 *</label>
          <select id="device-group" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
            <!-- 分组选项将通过JS动态添加 -->
          </select>
        </div>

        <div class="mb-4">
          <label for="device-type" class="block text-sm font-medium text-gray-700 mb-1">设备类型 *</label>
          <select id="device-type" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
            <option value="standard">标准</option>
            <option value="indoor">室内</option>
          </select>
        </div>

        <div class="mb-4">
          <label for="device-code" class="block text-sm font-medium text-gray-700 mb-1">链接码 *</label>
          <input type="text" id="device-code" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>
        
        <div class="mb-6">
          <label for="device-password" class="block text-sm font-medium text-gray-700 mb-1">连接密码 *</label>
          <input type="text" id="device-password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button type="button" id="cancel-btn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">取消</button>
          <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition shadow-sm">保存</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 新增/编辑分组模态框 -->
  <div id="group-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center fade-in">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-sm p-6 m-4">
      <div class="flex justify-between items-center mb-4">
        <h2 id="group-modal-title" class="text-xl font-bold text-dark">新增分组</h2>
        <button id="close-group-modal" class="text-gray-400 hover:text-gray-600">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>

      <form id="group-form">
        <input type="hidden" id="group-original-name">
        <div class="mb-6">
          <label for="group-name" class="block text-sm font-medium text-gray-700 mb-1">分组名称 *</label>
          <input type="text" id="group-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>

        <div class="flex justify-end space-x-3">
          <button type="button" id="cancel-group-btn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">取消</button>
          <button type="submit" id="group-submit-btn" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition shadow-sm">创建</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 确认删除设备模态框 -->
  <div id="confirm-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center fade-in">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-sm p-6 m-4">
      <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-danger/10 text-danger mb-4">
          <i class="fa fa-exclamation-triangle text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-dark mb-2">确认删除</h3>
        <p class="text-gray-500">您确定要删除此设备吗？此操作无法撤销。</p>
      </div>

      <div class="flex justify-center space-x-3">
        <button id="cancel-delete" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">取消</button>
        <button id="confirm-delete" class="px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 transition shadow-sm">删除</button>
      </div>
    </div>
  </div>

  <!-- 确认删除分组模态框 -->
  <div id="confirm-group-delete-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center fade-in">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-sm p-6 m-4">
      <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-danger/10 text-danger mb-4">
          <i class="fa fa-exclamation-triangle text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-dark mb-2">确认删除分组</h3>
        <p class="text-gray-500" id="group-delete-message">您确定要删除此分组吗？此操作无法撤销。</p>
      </div>

      <div class="flex justify-center space-x-3">
        <button id="cancel-group-delete" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">取消</button>
        <button id="confirm-group-delete" class="px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 transition shadow-sm">删除</button>
      </div>
    </div>
  </div>

  <!-- 复制成功提示 -->
  <div id="copy-toast" class="fixed bottom-6 right-6 bg-secondary text-white px-4 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center">
    <i class="fa fa-check-circle mr-2"></i>
    <span>复制成功！</span>
  </div>

  <script>
    // 数据模型
    class DataModel {
      constructor() {
        this.groups = JSON.parse(localStorage.getItem('remoteControlGroups')) || ['默认分组'];
        this.devices = JSON.parse(localStorage.getItem('remoteControlDevices')) || [];
      }
      
      // 保存数据到本地存储
      save() {
        localStorage.setItem('remoteControlGroups', JSON.stringify(this.groups));
        localStorage.setItem('remoteControlDevices', JSON.stringify(this.devices));
      }
      
      // 获取所有分组
      getGroups() {
        return [...this.groups];
      }
      
      // 添加新分组
      addGroup(name) {
        if (!this.groups.includes(name)) {
          this.groups.push(name);
          this.save();
          return true;
        }
        return false;
      }

      // 编辑分组名称
      editGroup(oldName, newName) {
        if (oldName === newName) return true;
        if (this.groups.includes(newName)) return false;

        const index = this.groups.indexOf(oldName);
        if (index !== -1) {
          this.groups[index] = newName;
          // 更新所有使用此分组的设备
          this.devices.forEach(device => {
            if (device.group === oldName) {
              device.group = newName;
            }
          });
          this.save();
          return true;
        }
        return false;
      }

      // 删除分组
      deleteGroup(name) {
        const index = this.groups.indexOf(name);
        if (index === -1 || index === 0) return false; // 不允许删除第一个分组（默认分组）

        this.groups.splice(index, 1);
        // 将使用此分组的设备移动到第一个分组
        const defaultGroup = this.groups[0];
        this.devices.forEach(device => {
          if (device.group === name) {
            device.group = defaultGroup;
          }
        });
        this.save();
        return true;
      }

      // 获取分组中的设备数量
      getGroupDeviceCount(groupName) {
        return this.devices.filter(device => device.group === groupName).length;
      }
      
      // 获取所有设备
      getDevices(group = 'all') {
        if (group === 'all') {
          return [...this.devices];
        }
        return this.devices.filter(device => device.group === group);
      }
      
      // 添加新设备
      addDevice(device) {
        const newDevice = {
          id: Date.now().toString(),
          type: 'standard', // 默认设备类型为标准
          ...device
        };
        this.devices.push(newDevice);
        this.save();
        return newDevice;
      }
      
      // 更新设备
      updateDevice(id, updatedData) {
        const index = this.devices.findIndex(device => device.id === id);
        if (index !== -1) {
          this.devices[index] = { ...this.devices[index], ...updatedData };
          this.save();
          return this.devices[index];
        }
        return null;
      }
      
      // 删除设备
      deleteDevice(id) {
        const initialLength = this.devices.length;
        this.devices = this.devices.filter(device => device.id !== id);
        this.save();
        return initialLength !== this.devices.length;
      }
      
      // 获取单个设备
      getDevice(id) {
        return this.devices.find(device => device.id === id) || null;
      }
    }

    // 应用控制器
    class AppController {
      constructor() {
        this.dataModel = new DataModel();
        this.currentGroup = 'all';
        this.currentPage = 'devices'; // 当前页面：devices 或 statistics
        this.initEventListeners();
        this.renderGroups();
        this.renderDevices();
      }
      
      // 初始化事件监听器
      initEventListeners() {
        // 模态框相关
        document.getElementById('add-device-btn').addEventListener('click', () => this.openDeviceModal());
        document.getElementById('close-modal').addEventListener('click', () => this.closeDeviceModal());
        document.getElementById('cancel-btn').addEventListener('click', () => this.closeDeviceModal());
        document.getElementById('device-form').addEventListener('submit', (e) => this.handleDeviceFormSubmit(e));
        
        // 分组相关
        document.getElementById('add-group-btn').addEventListener('click', () => this.openGroupModal());
        document.getElementById('close-group-modal').addEventListener('click', () => this.closeGroupModal());
        document.getElementById('cancel-group-btn').addEventListener('click', () => this.closeGroupModal());
        document.getElementById('group-form').addEventListener('submit', (e) => this.handleGroupFormSubmit(e));
        
        // 删除确认相关
        document.getElementById('cancel-delete').addEventListener('click', () => this.closeConfirmModal());
        document.getElementById('confirm-delete').addEventListener('click', () => this.handleDeleteConfirm());

        // 分组删除确认相关
        document.getElementById('cancel-group-delete').addEventListener('click', () => this.closeGroupDeleteModal());
        document.getElementById('confirm-group-delete').addEventListener('click', () => this.handleGroupDeleteConfirm());

        // 底部导航相关
        document.getElementById('nav-devices').addEventListener('click', () => this.switchPage('devices'));
        document.getElementById('nav-statistics').addEventListener('click', () => this.switchPage('statistics'));
        
        // 分组切换和管理
        document.getElementById('group-tabs').addEventListener('click', (e) => {
          // 编辑分组
          if (e.target.closest('.edit-group')) {
            e.stopPropagation();
            const group = e.target.closest('.edit-group').dataset.group;
            this.openEditGroupModal(group);
            return;
          }

          // 删除分组
          if (e.target.closest('.delete-group')) {
            e.stopPropagation();
            const group = e.target.closest('.delete-group').dataset.group;
            this.openGroupDeleteModal(group);
            return;
          }

          // 切换分组
          if (e.target.closest('[data-group]')) {
            const group = e.target.closest('[data-group]').dataset.group;
            this.switchGroup(group);
          }
        });
        
        // 设备列表事件委托
        document.getElementById('devices-container').addEventListener('click', (e) => {
          // 编辑设备
          if (e.target.closest('.edit-device')) {
            const id = e.target.closest('.device-card').dataset.id;
            this.openEditDeviceModal(id);
          }
          
          // 删除设备
          if (e.target.closest('.delete-device')) {
            const id = e.target.closest('.device-card').dataset.id;
            this.openConfirmDeleteModal(id);
          }
          
          // 复制链接码
          if (e.target.closest('.copy-code')) {
            const id = e.target.closest('.device-card').dataset.id;
            const device = this.dataModel.getDevice(id);
            if (device) {
              this.copyToClipboard(device.code);
            }
          }
          
          // 复制密码
          if (e.target.closest('.copy-password')) {
            const id = e.target.closest('.device-card').dataset.id;
            const device = this.dataModel.getDevice(id);
            if (device) {
              this.copyToClipboard(device.password);
            }
          }
        });
      }
      
      // 渲染分组选项卡
      renderGroups() {
        const groupTabs = document.getElementById('group-tabs');
        const deviceGroupSelect = document.getElementById('device-group');
        
        // 清除现有分组（保留"全部设备"）
        const allTab = groupTabs.querySelector('[data-group="all"]');
        groupTabs.innerHTML = '';
        groupTabs.appendChild(allTab);
        
        // 清空选择框
        deviceGroupSelect.innerHTML = '';
        
        // 添加分组到选项卡和选择框
        this.dataModel.getGroups().forEach(group => {
          // 添加到选项卡
          const tabContainer = document.createElement('div');
          tabContainer.className = 'relative group';

          const tab = document.createElement('button');
          tab.className = `px-4 py-2 rounded-t-lg border-b-2 border-transparent transition ${this.currentGroup === group ? 'tab-active' : ''}`;
          tab.dataset.group = group;
          tab.innerHTML = `<i class="fa fa-folder-o mr-1"></i> ${group}`;

          // 添加编辑和删除按钮
          const actionsDiv = document.createElement('div');
          actionsDiv.className = 'group-actions';

          const editBtn = document.createElement('button');
          editBtn.className = 'edit-group group-action-btn bg-warning hover:bg-warning/80';
          editBtn.dataset.group = group;
          editBtn.innerHTML = '<i class="fa fa-pencil"></i>';
          editBtn.title = '编辑分组';
          actionsDiv.appendChild(editBtn);

          // 只有非第一个分组才显示删除按钮
          if (this.dataModel.getGroups().indexOf(group) !== 0) {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'delete-group group-action-btn bg-danger hover:bg-danger/80';
            deleteBtn.dataset.group = group;
            deleteBtn.innerHTML = '<i class="fa fa-trash"></i>';
            deleteBtn.title = '删除分组';
            actionsDiv.appendChild(deleteBtn);
          }

          tabContainer.appendChild(actionsDiv);

          tabContainer.appendChild(tab);
          groupTabs.appendChild(tabContainer);

          // 添加到选择框
          const option = document.createElement('option');
          option.value = group;
          option.textContent = group;
          deviceGroupSelect.appendChild(option);
        });
      }
      
      // 渲染设备列表
      renderDevices() {
        const container = document.getElementById('devices-container');
        const devices = this.dataModel.getDevices(this.currentGroup);
        
        if (devices.length === 0) {
          container.innerHTML = `
            <div class="col-span-full flex items-center justify-center h-40 text-gray-400">
              <div class="text-center">
                <i class="fa fa-mobile text-5xl mb-3 opacity-30"></i>
                <p>当前分组暂无设备，请添加设备</p>
              </div>
            </div>
          `;
          return;
        }
        
        container.innerHTML = '';
        
        devices.forEach(device => {
          const card = document.createElement('div');
          card.className = 'device-card bg-white rounded-xl shadow-sm p-5 card-hover border border-gray-100';
          card.dataset.id = device.id;
          
          card.innerHTML = `
            <div class="flex justify-between items-start mb-4">
              <h3 class="font-bold text-lg text-dark">${device.name}</h3>
              <div class="flex flex-col items-end space-y-1">
                <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">${device.group}</span>
                <span class="text-xs ${device.type === 'indoor' ? 'bg-blue-100 text-blue-600' : 'bg-green-100 text-green-600'} px-2 py-1 rounded-full">
                  ${device.type === 'indoor' ? '室内' : '标准'}
                </span>
              </div>
            </div>
            
            <div class="space-y-3 mb-4">
              <div class="flex items-center">
                <span class="text-sm text-gray-500 w-16">链接码:</span>
                <span class="text-sm flex-grow truncate">${device.code}</span>
                <button class="copy-code text-primary hover:text-primary/80 p-1 ml-1 btn-effect">
                  <i class="fa fa-copy"></i>
                </button>
              </div>
              
              <div class="flex items-center">
                <span class="text-sm text-gray-500 w-16">密码:</span>
                <span class="text-sm flex-grow truncate">${device.password}</span>
                <button class="copy-password text-primary hover:text-primary/80 p-1 ml-1 btn-effect">
                  <i class="fa fa-copy"></i>
                </button>
              </div>
            </div>
            
            <div class="flex justify-end space-x-2 pt-2 border-t border-gray-100">
              <button class="edit-device text-warning hover:text-warning/80 p-2 btn-effect">
                <i class="fa fa-pencil"></i>
              </button>
              <button class="delete-device text-danger hover:text-danger/80 p-2 btn-effect">
                <i class="fa fa-trash"></i>
              </button>
            </div>
          `;
          
          container.appendChild(card);
        });
      }
      
      // 切换分组
      switchGroup(group) {
        this.currentGroup = group;
        
        // 更新选项卡样式
        document.querySelectorAll('#group-tabs [data-group]').forEach(tab => {
          if (tab.dataset.group === group) {
            tab.classList.add('tab-active');
          } else {
            tab.classList.remove('tab-active');
          }
        });
        
        // 重新渲染设备
        this.renderDevices();
      }
      
      // 打开设备模态框（新增）
      openDeviceModal() {
        document.getElementById('modal-title').textContent = '新增设备';
        document.getElementById('device-form').reset();
        document.getElementById('device-id').value = '';
        document.getElementById('device-modal').classList.remove('hidden');
        document.getElementById('device-modal').classList.add('flex');
        document.getElementById('device-name').focus();
      }
      
      // 打开设备模态框（编辑）
      openEditDeviceModal(id) {
        const device = this.dataModel.getDevice(id);
        if (!device) return;
        
        document.getElementById('modal-title').textContent = '编辑设备';
        document.getElementById('device-id').value = device.id;
        document.getElementById('device-name').value = device.name;
        document.getElementById('device-group').value = device.group;
        document.getElementById('device-type').value = device.type || 'standard';
        document.getElementById('device-code').value = device.code;
        document.getElementById('device-password').value = device.password;
        
        document.getElementById('device-modal').classList.remove('hidden');
        document.getElementById('device-modal').classList.add('flex');
        document.getElementById('device-name').focus();
      }
      
      // 关闭设备模态框
      closeDeviceModal() {
        document.getElementById('device-modal').classList.add('hidden');
        document.getElementById('device-modal').classList.remove('flex');
      }
      
      // 处理设备表单提交
      handleDeviceFormSubmit(e) {
        e.preventDefault();
        
        const id = document.getElementById('device-id').value;
        const deviceData = {
          name: document.getElementById('device-name').value,
          group: document.getElementById('device-group').value,
          type: document.getElementById('device-type').value,
          code: document.getElementById('device-code').value,
          password: document.getElementById('device-password').value
        };
        
        if (id) {
          // 更新设备
          this.dataModel.updateDevice(id, deviceData);
        } else {
          // 添加新设备
          this.dataModel.addDevice(deviceData);
        }
        
        this.closeDeviceModal();
        this.renderDevices();
        // 如果当前在统计页面，更新统计信息
        if (this.currentPage === 'statistics') {
          this.renderStatistics();
        }
      }
      
      // 打开分组模态框（新增）
      openGroupModal() {
        document.getElementById('group-modal-title').textContent = '新增分组';
        document.getElementById('group-submit-btn').textContent = '创建';
        document.getElementById('group-form').reset();
        document.getElementById('group-original-name').value = '';
        document.getElementById('group-modal').classList.remove('hidden');
        document.getElementById('group-modal').classList.add('flex');
        document.getElementById('group-name').focus();
      }

      // 打开分组模态框（编辑）
      openEditGroupModal(groupName) {
        document.getElementById('group-modal-title').textContent = '编辑分组';
        document.getElementById('group-submit-btn').textContent = '保存';
        document.getElementById('group-original-name').value = groupName;
        document.getElementById('group-name').value = groupName;
        document.getElementById('group-modal').classList.remove('hidden');
        document.getElementById('group-modal').classList.add('flex');
        document.getElementById('group-name').focus();
        document.getElementById('group-name').select();
      }

      // 关闭分组模态框
      closeGroupModal() {
        document.getElementById('group-modal').classList.add('hidden');
        document.getElementById('group-modal').classList.remove('flex');
      }

      // 打开删除分组确认模态框
      openGroupDeleteModal(groupName) {
        const deviceCount = this.dataModel.getGroupDeviceCount(groupName);
        const message = deviceCount > 0
          ? `您确定要删除分组"${groupName}"吗？该分组下有 ${deviceCount} 个设备，删除后这些设备将移动到"默认分组"。此操作无法撤销。`
          : `您确定要删除分组"${groupName}"吗？此操作无法撤销。`;

        document.getElementById('group-delete-message').textContent = message;
        this.currentDeleteGroupName = groupName;
        document.getElementById('confirm-group-delete-modal').classList.remove('hidden');
        document.getElementById('confirm-group-delete-modal').classList.add('flex');
      }

      // 关闭删除分组确认模态框
      closeGroupDeleteModal() {
        this.currentDeleteGroupName = null;
        document.getElementById('confirm-group-delete-modal').classList.add('hidden');
        document.getElementById('confirm-group-delete-modal').classList.remove('flex');
      }

      // 处理删除分组确认
      handleGroupDeleteConfirm() {
        if (this.currentDeleteGroupName) {
          const success = this.dataModel.deleteGroup(this.currentDeleteGroupName);
          if (success) {
            // 如果删除的是当前显示的分组，切换到"全部设备"
            if (this.currentGroup === this.currentDeleteGroupName) {
              this.currentGroup = 'all';
            }
            this.renderGroups();
            this.renderDevices();
            // 如果当前在统计页面，更新统计信息
            if (this.currentPage === 'statistics') {
              this.renderStatistics();
            }
          }
          this.closeGroupDeleteModal();
        }
      }
      
      // 处理分组表单提交
      handleGroupFormSubmit(e) {
        e.preventDefault();

        const groupName = document.getElementById('group-name').value.trim();
        const originalName = document.getElementById('group-original-name').value;

        if (groupName) {
          let success = false;

          if (originalName) {
            // 编辑分组
            success = this.dataModel.editGroup(originalName, groupName);
            if (!success) {
              alert('该分组名称已存在！');
              return;
            }
            // 如果当前显示的是被编辑的分组，更新当前分组
            if (this.currentGroup === originalName) {
              this.currentGroup = groupName;
            }
          } else {
            // 新增分组
            success = this.dataModel.addGroup(groupName);
            if (!success) {
              alert('该分组名称已存在！');
              return;
            }
          }

          this.renderGroups();
          this.renderDevices();
          this.closeGroupModal();
          // 如果当前在统计页面，更新统计信息
          if (this.currentPage === 'statistics') {
            this.renderStatistics();
          }
        }
      }
      
      // 打开删除确认模态框
      openConfirmDeleteModal(id) {
        this.currentDeleteId = id;
        document.getElementById('confirm-modal').classList.remove('hidden');
        document.getElementById('confirm-modal').classList.add('flex');
      }
      
      // 关闭删除确认模态框
      closeConfirmModal() {
        this.currentDeleteId = null;
        document.getElementById('confirm-modal').classList.add('hidden');
        document.getElementById('confirm-modal').classList.remove('flex');
      }
      
      // 处理删除确认
      handleDeleteConfirm() {
        if (this.currentDeleteId) {
          this.dataModel.deleteDevice(this.currentDeleteId);
          this.renderDevices();
          this.closeConfirmModal();
          // 如果当前在统计页面，更新统计信息
          if (this.currentPage === 'statistics') {
            this.renderStatistics();
          }
        }
      }
      
      // 复制到剪贴板
      copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
          // 显示复制成功提示
          const toast = document.getElementById('copy-toast');
          toast.classList.remove('translate-y-20', 'opacity-0');

          setTimeout(() => {
            toast.classList.add('translate-y-20', 'opacity-0');
          }, 2000);
        });
      }

      // 切换页面
      switchPage(page) {
        this.currentPage = page;

        // 更新导航按钮状态
        document.querySelectorAll('.nav-tab').forEach(tab => {
          tab.classList.remove('nav-tab-active');
        });

        if (page === 'devices') {
          document.getElementById('nav-devices').classList.add('nav-tab-active');
          document.getElementById('devices-page').classList.remove('hidden');
          document.getElementById('statistics-page').classList.add('hidden');
        } else if (page === 'statistics') {
          document.getElementById('nav-statistics').classList.add('nav-tab-active');
          document.getElementById('devices-page').classList.add('hidden');
          document.getElementById('statistics-page').classList.remove('hidden');
          this.renderStatistics();
        }
      }

      // 渲染统计信息
      renderStatistics() {
        const devices = this.dataModel.getDevices();
        const groups = this.dataModel.getGroups();

        // 基础统计
        document.getElementById('total-devices').textContent = devices.length;
        document.getElementById('total-groups').textContent = groups.length;

        // 设备类型统计
        const indoorCount = devices.filter(device => device.type === 'indoor').length;
        const standardCount = devices.filter(device => device.type === 'standard' || !device.type).length;
        document.getElementById('indoor-devices').textContent = indoorCount;
        document.getElementById('standard-devices').textContent = standardCount;

        // 分组详细统计
        this.renderGroupStatistics();
      }

      // 渲染分组统计
      renderGroupStatistics() {
        const container = document.getElementById('group-statistics');
        const groups = this.dataModel.getGroups();

        container.innerHTML = '';

        groups.forEach(group => {
          const groupDevices = this.dataModel.getDevices(group);
          const indoorCount = groupDevices.filter(device => device.type === 'indoor').length;
          const standardCount = groupDevices.filter(device => device.type === 'standard' || !device.type).length;

          const groupRow = document.createElement('div');
          groupRow.className = 'flex items-center justify-between p-4 bg-gray-50 rounded-lg';

          groupRow.innerHTML = `
            <div class="flex items-center space-x-3">
              <div class="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                <i class="fa fa-folder-o text-primary"></i>
              </div>
              <div>
                <h4 class="font-medium text-dark">${group}</h4>
                <p class="text-sm text-gray-500">共 ${groupDevices.length} 个设备</p>
              </div>
            </div>
            <div class="flex space-x-6 text-sm">
              <div class="text-center">
                <p class="text-blue-600 font-bold">${indoorCount}</p>
                <p class="text-gray-500">室内</p>
              </div>
              <div class="text-center">
                <p class="text-green-600 font-bold">${standardCount}</p>
                <p class="text-gray-500">标准</p>
              </div>
            </div>
          `;

          container.appendChild(groupRow);
        });

        if (groups.length === 0) {
          container.innerHTML = `
            <div class="text-center py-8 text-gray-400">
              <i class="fa fa-folder-open-o text-4xl mb-3 opacity-30"></i>
              <p>暂无分组数据</p>
            </div>
          `;
        }
      }
    }

    // 初始化应用
    document.addEventListener('DOMContentLoaded', () => {
      new AppController();
    });
  </script>
</body>
</html>
