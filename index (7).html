<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>远程控制码管理中心</title>
  <!-- 引入Tailwind CSS -->
  <script src="https://cdn.tailwindcss.com"></script>
  <!-- 引入Font Awesome -->
  <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
  
  <!-- 配置Tailwind自定义颜色和字体 -->
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: '#165DFF',
            secondary: '#36D399',
            warning: '#FF9F1C',
            danger: '#F87272',
            dark: '#1E293B',
            light: '#F8FAFC'
          },
          fontFamily: {
            inter: ['Inter', 'system-ui', 'sans-serif'],
          },
        },
      }
    }
  </script>
  
  <style type="text/tailwindcss">
    @layer utilities {
      .content-auto {
        content-visibility: auto;
      }
      .tab-active {
        @apply border-primary text-primary font-medium;
      }
      .card-hover {
        @apply transition-all duration-300 hover:shadow-lg hover:-translate-y-1;
      }
      .btn-effect {
        @apply transition-all duration-200 transform hover:scale-105 active:scale-95;
      }
      .fade-in {
        animation: fadeIn 0.3s ease-in-out;
      }
      @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
      }
    }
  </style>
</head>

<body class="font-inter bg-gray-50 text-dark min-h-screen flex flex-col">
  <!-- 顶部导航栏 -->
  <header class="bg-white shadow-sm sticky top-0 z-50">
    <div class="container mx-auto px-4 py-4 flex flex-wrap items-center justify-between">
      <div class="flex items-center space-x-2">
        <i class="fa fa-server text-primary text-2xl"></i>
        <h1 class="text-xl md:text-2xl font-bold text-primary">远程控制码管理中心</h1>
      </div>
      
      <div class="flex items-center space-x-3 mt-2 sm:mt-0">
        <button id="add-group-btn" class="btn-effect flex items-center px-3 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg text-sm">
          <i class="fa fa-folder-open-o mr-1"></i> 新增分组
        </button>
        <button id="add-device-btn" class="btn-effect flex items-center px-4 py-2 bg-primary text-white rounded-lg text-sm shadow-sm hover:bg-primary/90">
          <i class="fa fa-plus mr-1"></i> 新增设备
        </button>
      </div>
    </div>
  </header>

  <!-- 主内容区 -->
  <main class="flex-grow container mx-auto px-4 py-6">
    <!-- 分组选项卡 -->
    <div class="mb-6 overflow-x-auto scrollbar-hide">
      <div class="flex space-x-1 pb-2 min-w-max" id="group-tabs">
        <button class="tab-active px-4 py-2 rounded-t-lg border-b-2 border-transparent" data-group="all">
          <i class="fa fa-th mr-1"></i> 全部设备
        </button>
        <!-- 分组将通过JS动态添加 -->
      </div>
    </div>
    
    <!-- 设备列表区域 -->
    <div id="devices-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
      <!-- 设备卡片将通过JS动态添加 -->
      <div class="col-span-full flex items-center justify-center h-40 text-gray-400">
        <div class="text-center">
          <i class="fa fa-mobile text-5xl mb-3 opacity-30"></i>
          <p>暂无设备数据，请添加设备</p>
        </div>
      </div>
    </div>
  </main>

  <!-- 页脚 -->
  <footer class="bg-white border-t mt-8 py-4">
    <div class="container mx-auto px-4 text-center text-gray-500 text-sm">
      <p>远程控制码管理中心 &copy; 2023</p>
    </div>
  </footer>

  <!-- 新增/编辑设备模态框 -->
  <div id="device-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center fade-in">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-md p-6 m-4 transform transition-all">
      <div class="flex justify-between items-center mb-4">
        <h2 id="modal-title" class="text-xl font-bold text-dark">新增设备</h2>
        <button id="close-modal" class="text-gray-400 hover:text-gray-600">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>
      
      <form id="device-form">
        <input type="hidden" id="device-id">
        
        <div class="mb-4">
          <label for="device-name" class="block text-sm font-medium text-gray-700 mb-1">设备名称 *</label>
          <input type="text" id="device-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>
        
        <div class="mb-4">
          <label for="device-group" class="block text-sm font-medium text-gray-700 mb-1">所属分组 *</label>
          <select id="device-group" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
            <!-- 分组选项将通过JS动态添加 -->
          </select>
        </div>
        
        <div class="mb-4">
          <label for="device-code" class="block text-sm font-medium text-gray-700 mb-1">链接码 *</label>
          <input type="text" id="device-code" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>
        
        <div class="mb-6">
          <label for="device-password" class="block text-sm font-medium text-gray-700 mb-1">连接密码 *</label>
          <input type="text" id="device-password" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button type="button" id="cancel-btn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">取消</button>
          <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition shadow-sm">保存</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 新增分组模态框 -->
  <div id="group-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center fade-in">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-sm p-6 m-4">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-bold text-dark">新增分组</h2>
        <button id="close-group-modal" class="text-gray-400 hover:text-gray-600">
          <i class="fa fa-times text-xl"></i>
        </button>
      </div>
      
      <form id="group-form">
        <div class="mb-6">
          <label for="group-name" class="block text-sm font-medium text-gray-700 mb-1">分组名称 *</label>
          <input type="text" id="group-name" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary/50 focus:border-primary outline-none transition" required>
        </div>
        
        <div class="flex justify-end space-x-3">
          <button type="button" id="cancel-group-btn" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">取消</button>
          <button type="submit" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 transition shadow-sm">创建</button>
        </div>
      </form>
    </div>
  </div>

  <!-- 确认删除模态框 -->
  <div id="confirm-modal" class="fixed inset-0 bg-black/50 z-50 hidden items-center justify-center fade-in">
    <div class="bg-white rounded-xl shadow-xl w-full max-w-sm p-6 m-4">
      <div class="text-center mb-6">
        <div class="inline-flex items-center justify-center w-16 h-16 rounded-full bg-danger/10 text-danger mb-4">
          <i class="fa fa-exclamation-triangle text-2xl"></i>
        </div>
        <h3 class="text-lg font-bold text-dark mb-2">确认删除</h3>
        <p class="text-gray-500">您确定要删除此设备吗？此操作无法撤销。</p>
      </div>
      
      <div class="flex justify-center space-x-3">
        <button id="cancel-delete" class="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition">取消</button>
        <button id="confirm-delete" class="px-4 py-2 bg-danger text-white rounded-lg hover:bg-danger/90 transition shadow-sm">删除</button>
      </div>
    </div>
  </div>

  <!-- 复制成功提示 -->
  <div id="copy-toast" class="fixed bottom-6 right-6 bg-secondary text-white px-4 py-3 rounded-lg shadow-lg transform translate-y-20 opacity-0 transition-all duration-300 flex items-center">
    <i class="fa fa-check-circle mr-2"></i>
    <span>复制成功！</span>
  </div>

  <script>
    // 数据模型
    class DataModel {
      constructor() {
        this.groups = JSON.parse(localStorage.getItem('remoteControlGroups')) || ['默认分组'];
        this.devices = JSON.parse(localStorage.getItem('remoteControlDevices')) || [];
      }
      
      // 保存数据到本地存储
      save() {
        localStorage.setItem('remoteControlGroups', JSON.stringify(this.groups));
        localStorage.setItem('remoteControlDevices', JSON.stringify(this.devices));
      }
      
      // 获取所有分组
      getGroups() {
        return [...this.groups];
      }
      
      // 添加新分组
      addGroup(name) {
        if (!this.groups.includes(name)) {
          this.groups.push(name);
          this.save();
          return true;
        }
        return false;
      }
      
      // 获取所有设备
      getDevices(group = 'all') {
        if (group === 'all') {
          return [...this.devices];
        }
        return this.devices.filter(device => device.group === group);
      }
      
      // 添加新设备
      addDevice(device) {
        const newDevice = {
          id: Date.now().toString(),
          ...device
        };
        this.devices.push(newDevice);
        this.save();
        return newDevice;
      }
      
      // 更新设备
      updateDevice(id, updatedData) {
        const index = this.devices.findIndex(device => device.id === id);
        if (index !== -1) {
          this.devices[index] = { ...this.devices[index], ...updatedData };
          this.save();
          return this.devices[index];
        }
        return null;
      }
      
      // 删除设备
      deleteDevice(id) {
        const initialLength = this.devices.length;
        this.devices = this.devices.filter(device => device.id !== id);
        this.save();
        return initialLength !== this.devices.length;
      }
      
      // 获取单个设备
      getDevice(id) {
        return this.devices.find(device => device.id === id) || null;
      }
    }

    // 应用控制器
    class AppController {
      constructor() {
        this.dataModel = new DataModel();
        this.currentGroup = 'all';
        this.initEventListeners();
        this.renderGroups();
        this.renderDevices();
      }
      
      // 初始化事件监听器
      initEventListeners() {
        // 模态框相关
        document.getElementById('add-device-btn').addEventListener('click', () => this.openDeviceModal());
        document.getElementById('close-modal').addEventListener('click', () => this.closeDeviceModal());
        document.getElementById('cancel-btn').addEventListener('click', () => this.closeDeviceModal());
        document.getElementById('device-form').addEventListener('submit', (e) => this.handleDeviceFormSubmit(e));
        
        // 分组相关
        document.getElementById('add-group-btn').addEventListener('click', () => this.openGroupModal());
        document.getElementById('close-group-modal').addEventListener('click', () => this.closeGroupModal());
        document.getElementById('cancel-group-btn').addEventListener('click', () => this.closeGroupModal());
        document.getElementById('group-form').addEventListener('submit', (e) => this.handleGroupFormSubmit(e));
        
        // 删除确认相关
        document.getElementById('cancel-delete').addEventListener('click', () => this.closeConfirmModal());
        document.getElementById('confirm-delete').addEventListener('click', () => this.handleDeleteConfirm());
        
        // 分组切换
        document.getElementById('group-tabs').addEventListener('click', (e) => {
          if (e.target.closest('[data-group]')) {
            const group = e.target.closest('[data-group]').dataset.group;
            this.switchGroup(group);
          }
        });
        
        // 设备列表事件委托
        document.getElementById('devices-container').addEventListener('click', (e) => {
          // 编辑设备
          if (e.target.closest('.edit-device')) {
            const id = e.target.closest('.device-card').dataset.id;
            this.openEditDeviceModal(id);
          }
          
          // 删除设备
          if (e.target.closest('.delete-device')) {
            const id = e.target.closest('.device-card').dataset.id;
            this.openConfirmDeleteModal(id);
          }
          
          // 复制链接码
          if (e.target.closest('.copy-code')) {
            const id = e.target.closest('.device-card').dataset.id;
            const device = this.dataModel.getDevice(id);
            if (device) {
              this.copyToClipboard(device.code);
            }
          }
          
          // 复制密码
          if (e.target.closest('.copy-password')) {
            const id = e.target.closest('.device-card').dataset.id;
            const device = this.dataModel.getDevice(id);
            if (device) {
              this.copyToClipboard(device.password);
            }
          }
        });
      }
      
      // 渲染分组选项卡
      renderGroups() {
        const groupTabs = document.getElementById('group-tabs');
        const deviceGroupSelect = document.getElementById('device-group');
        
        // 清除现有分组（保留"全部设备"）
        const allTab = groupTabs.querySelector('[data-group="all"]');
        groupTabs.innerHTML = '';
        groupTabs.appendChild(allTab);
        
        // 清空选择框
        deviceGroupSelect.innerHTML = '';
        
        // 添加分组到选项卡和选择框
        this.dataModel.getGroups().forEach(group => {
          // 添加到选项卡
          const tab = document.createElement('button');
          tab.className = `px-4 py-2 rounded-t-lg border-b-2 border-transparent transition ${this.currentGroup === group ? 'tab-active' : ''}`;
          tab.dataset.group = group;
          tab.innerHTML = `<i class="fa fa-folder-o mr-1"></i> ${group}`;
          groupTabs.appendChild(tab);
          
          // 添加到选择框
          const option = document.createElement('option');
          option.value = group;
          option.textContent = group;
          deviceGroupSelect.appendChild(option);
        });
      }
      
      // 渲染设备列表
      renderDevices() {
        const container = document.getElementById('devices-container');
        const devices = this.dataModel.getDevices(this.currentGroup);
        
        if (devices.length === 0) {
          container.innerHTML = `
            <div class="col-span-full flex items-center justify-center h-40 text-gray-400">
              <div class="text-center">
                <i class="fa fa-mobile text-5xl mb-3 opacity-30"></i>
                <p>当前分组暂无设备，请添加设备</p>
              </div>
            </div>
          `;
          return;
        }
        
        container.innerHTML = '';
        
        devices.forEach(device => {
          const card = document.createElement('div');
          card.className = 'device-card bg-white rounded-xl shadow-sm p-5 card-hover border border-gray-100';
          card.dataset.id = device.id;
          
          card.innerHTML = `
            <div class="flex justify-between items-start mb-4">
              <h3 class="font-bold text-lg text-dark">${device.name}</h3>
              <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full">${device.group}</span>
            </div>
            
            <div class="space-y-3 mb-4">
              <div class="flex items-center">
                <span class="text-sm text-gray-500 w-16">链接码:</span>
                <span class="text-sm flex-grow truncate">${device.code}</span>
                <button class="copy-code text-primary hover:text-primary/80 p-1 ml-1 btn-effect">
                  <i class="fa fa-copy"></i>
                </button>
              </div>
              
              <div class="flex items-center">
                <span class="text-sm text-gray-500 w-16">密码:</span>
                <span class="text-sm flex-grow truncate">${device.password}</span>
                <button class="copy-password text-primary hover:text-primary/80 p-1 ml-1 btn-effect">
                  <i class="fa fa-copy"></i>
                </button>
              </div>
            </div>
            
            <div class="flex justify-end space-x-2 pt-2 border-t border-gray-100">
              <button class="edit-device text-warning hover:text-warning/80 p-2 btn-effect">
                <i class="fa fa-pencil"></i>
              </button>
              <button class="delete-device text-danger hover:text-danger/80 p-2 btn-effect">
                <i class="fa fa-trash"></i>
              </button>
            </div>
          `;
          
          container.appendChild(card);
        });
      }
      
      // 切换分组
      switchGroup(group) {
        this.currentGroup = group;
        
        // 更新选项卡样式
        document.querySelectorAll('#group-tabs [data-group]').forEach(tab => {
          if (tab.dataset.group === group) {
            tab.classList.add('tab-active');
          } else {
            tab.classList.remove('tab-active');
          }
        });
        
        // 重新渲染设备
        this.renderDevices();
      }
      
      // 打开设备模态框（新增）
      openDeviceModal() {
        document.getElementById('modal-title').textContent = '新增设备';
        document.getElementById('device-form').reset();
        document.getElementById('device-id').value = '';
        document.getElementById('device-modal').classList.remove('hidden');
        document.getElementById('device-modal').classList.add('flex');
        document.getElementById('device-name').focus();
      }
      
      // 打开设备模态框（编辑）
      openEditDeviceModal(id) {
        const device = this.dataModel.getDevice(id);
        if (!device) return;
        
        document.getElementById('modal-title').textContent = '编辑设备';
        document.getElementById('device-id').value = device.id;
        document.getElementById('device-name').value = device.name;
        document.getElementById('device-group').value = device.group;
        document.getElementById('device-code').value = device.code;
        document.getElementById('device-password').value = device.password;
        
        document.getElementById('device-modal').classList.remove('hidden');
        document.getElementById('device-modal').classList.add('flex');
        document.getElementById('device-name').focus();
      }
      
      // 关闭设备模态框
      closeDeviceModal() {
        document.getElementById('device-modal').classList.add('hidden');
        document.getElementById('device-modal').classList.remove('flex');
      }
      
      // 处理设备表单提交
      handleDeviceFormSubmit(e) {
        e.preventDefault();
        
        const id = document.getElementById('device-id').value;
        const deviceData = {
          name: document.getElementById('device-name').value,
          group: document.getElementById('device-group').value,
          code: document.getElementById('device-code').value,
          password: document.getElementById('device-password').value
        };
        
        if (id) {
          // 更新设备
          this.dataModel.updateDevice(id, deviceData);
        } else {
          // 添加新设备
          this.dataModel.addDevice(deviceData);
        }
        
        this.closeDeviceModal();
        this.renderDevices();
      }
      
      // 打开分组模态框
      openGroupModal() {
        document.getElementById('group-form').reset();
        document.getElementById('group-modal').classList.remove('hidden');
        document.getElementById('group-modal').classList.add('flex');
        document.getElementById('group-name').focus();
      }
      
      // 关闭分组模态框
      closeGroupModal() {
        document.getElementById('group-modal').classList.add('hidden');
        document.getElementById('group-modal').classList.remove('flex');
      }
      
      // 处理分组表单提交
      handleGroupFormSubmit(e) {
        e.preventDefault();
        
        const groupName = document.getElementById('group-name').value.trim();
        if (groupName) {
          const added = this.dataModel.addGroup(groupName);
          if (added) {
            this.renderGroups();
            this.closeGroupModal();
          } else {
            alert('该分组名称已存在！');
          }
        }
      }
      
      // 打开删除确认模态框
      openConfirmDeleteModal(id) {
        this.currentDeleteId = id;
        document.getElementById('confirm-modal').classList.remove('hidden');
        document.getElementById('confirm-modal').classList.add('flex');
      }
      
      // 关闭删除确认模态框
      closeConfirmModal() {
        this.currentDeleteId = null;
        document.getElementById('confirm-modal').classList.add('hidden');
        document.getElementById('confirm-modal').classList.remove('flex');
      }
      
      // 处理删除确认
      handleDeleteConfirm() {
        if (this.currentDeleteId) {
          this.dataModel.deleteDevice(this.currentDeleteId);
          this.renderDevices();
          this.closeConfirmModal();
        }
      }
      
      // 复制到剪贴板
      copyToClipboard(text) {
        navigator.clipboard.writeText(text).then(() => {
          // 显示复制成功提示
          const toast = document.getElementById('copy-toast');
          toast.classList.remove('translate-y-20', 'opacity-0');
          
          setTimeout(() => {
            toast.classList.add('translate-y-20', 'opacity-0');
          }, 2000);
        });
      }
    }

    // 初始化应用
    document.addEventListener('DOMContentLoaded', () => {
      new AppController();
    });
  </script>
</body>
</html>
